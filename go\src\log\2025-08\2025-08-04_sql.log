{"level":"dev.info","ts":"[2025-08-04 11:15:41.012]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COUNT(*) as total FROM (\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 ORDER BY baa.createtime DESC) as count_query, []","duration":"30.8136ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-04 11:15:41.048]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 ORDER BY baa.createtime DESC LIMIT 20 OFFSET 0, []","duration":"31.8978ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-04 11:15:41.060]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT user_id, MAX(CASE WHEN paid_at IS NOT NULL THEN UNIX_TIMESTAMP(paid_at) ELSE NULL END) as last_repay_time, MIN(CASE WHEN status = 0 THEN UNIX_TIMESTAMP(due_date) ELSE NULL END) as bill_due_time FROM `business_repayment_bills` WHERE `user_id` IN (?) GROUP BY user_id, [1]","duration":"11.2649ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-04 11:15:41.089]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT user_id, SUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as borrowing_order_count, COUNT(*) as total_order_count FROM `business_loan_orders` WHERE `user_id` IN (?) GROUP BY user_id, [1]","duration":"40.7029ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-08-04 11:16:13.122]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COUNT(*) as total FROM (\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND COALESCE(order_filter.loanTime, 0) >= UNIX_TIMESTAMP(?) AND COALESCE(order_filter.loanTime, 0) <= UNIX_TIMESTAMP(?) ORDER BY baa.createtime DESC) as count_query, [2025-08-02 2025-08-02]","duration":"12.6093ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-04 11:16:13.136]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND COALESCE(order_filter.loanTime, 0) >= UNIX_TIMESTAMP(?) AND COALESCE(order_filter.loanTime, 0) <= UNIX_TIMESTAMP(?) ORDER BY baa.createtime DESC LIMIT 20 OFFSET 0, [2025-08-02 2025-08-02]","duration":"14.9455ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-04 11:21:05.270]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COUNT(*) as total FROM (\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND (order_filter.loanTime IS NULL OR order_filter.loanTime >= UNIX_TIMESTAMP(?)) AND (order_filter.loanTime IS NULL OR order_filter.loanTime <= UNIX_TIMESTAMP(?)) ORDER BY baa.createtime DESC) as count_query, [2025-08-02 2025-08-02]","duration":"34.7581ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-04 11:21:05.303]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND (order_filter.loanTime IS NULL OR order_filter.loanTime >= UNIX_TIMESTAMP(?)) AND (order_filter.loanTime IS NULL OR order_filter.loanTime <= UNIX_TIMESTAMP(?)) ORDER BY baa.createtime DESC LIMIT 20 OFFSET 0, [2025-08-02 2025-08-02]","duration":"31.0086ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-04 11:21:06.520]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COUNT(*) as total FROM (\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND (order_filter.loanTime IS NULL OR order_filter.loanTime >= UNIX_TIMESTAMP(?)) AND (order_filter.loanTime IS NULL OR order_filter.loanTime <= UNIX_TIMESTAMP(?)) ORDER BY baa.createtime DESC) as count_query, [2025-08-02 2025-08-02]","duration":"9.0541ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-04 11:21:06.538]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND (order_filter.loanTime IS NULL OR order_filter.loanTime >= UNIX_TIMESTAMP(?)) AND (order_filter.loanTime IS NULL OR order_filter.loanTime <= UNIX_TIMESTAMP(?)) ORDER BY baa.createtime DESC LIMIT 20 OFFSET 0, [2025-08-02 2025-08-02]","duration":"16.5747ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 11:26:18.773]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COUNT(*) as total FROM (\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime >= UNIX_TIMESTAMP(?) AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime <= UNIX_TIMESTAMP(?) ORDER BY baa.createtime DESC) as count_query, [2025-08-02 2025-08-02]","duration":"16.7829ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-04 11:26:18.829]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime >= UNIX_TIMESTAMP(?) AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime <= UNIX_TIMESTAMP(?) ORDER BY baa.createtime DESC LIMIT 20 OFFSET 0, [2025-08-02 2025-08-02]","duration":"56.1418ms","duration_ms":56}
{"level":"dev.info","ts":"[2025-08-04 11:26:20.492]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COUNT(*) as total FROM (\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime >= UNIX_TIMESTAMP(?) AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime <= UNIX_TIMESTAMP(?) ORDER BY baa.createtime DESC) as count_query, [2025-08-02 2025-08-02]","duration":"12.2097ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-04 11:26:20.505]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime >= UNIX_TIMESTAMP(?) AND order_filter.loanTime IS NOT NULL AND order_filter.loanTime <= UNIX_TIMESTAMP(?) ORDER BY baa.createtime DESC LIMIT 20 OFFSET 0, [2025-08-02 2025-08-02]","duration":"13.6379ms","duration_ms":13}
