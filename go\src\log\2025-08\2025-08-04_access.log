{"level":"dev.info","ts":"[2025-08-04 11:15:41.092]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"185871f272a30f9cd4d92aee","method":"POST","url":"/business/customer/customercontroller/listCustomers","query":"","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.1108783,"request_size":38,"response_size":1528}
{"level":"dev.info","ts":"[2025-08-04 11:16:13.136]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"185871f9edb14364facb2a63","method":"POST","url":"/business/customer/customercontroller/listCustomers","query":"","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0275548,"request_size":102,"response_size":412}
{"level":"dev.info","ts":"[2025-08-04 11:21:05.304]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1858723df186301421fcdbc5","method":"POST","url":"/business/customer/customercontroller/listCustomers","query":"","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0686208,"request_size":102,"response_size":412}
{"level":"dev.info","ts":"[2025-08-04 11:21:06.539]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1858723e3dc658a0263b34b1","method":"POST","url":"/business/customer/customercontroller/listCustomers","query":"","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0285118,"request_size":102,"response_size":412}
{"level":"dev.info","ts":"[2025-08-04 11:26:18.831]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18587286f100a8108040ec4e","method":"POST","url":"/business/customer/customercontroller/listCustomers","query":"","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0749983,"request_size":102,"response_size":412}
{"level":"dev.info","ts":"[2025-08-04 11:26:20.505]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1858728757c404d45eb5a0b5","method":"POST","url":"/business/customer/customercontroller/listCustomers","query":"","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0263549,"request_size":102,"response_size":412}
